"""
甲方给了部分数据
状态空间:路由网络的协议类型以及网络规模等参数
行为空间:由AODV路由发现时间和OLSR拓扑控制间隙组成，因为跟队不同的协议两个参数是互斥的，所以共有20个可选动作
"""

import logging
import numpy as np
import sqlite3
import os


class RouteDecideEnv:
    def __init__(self, db_name='train_data.db', seed = 0):
        # 状态空间和行为空间
        #self.observation_dim = [3, 8, 10]         # 定义状态空间每个维度的取值范围
        # self.action_dim = [8, 6, 3, 2]         # 定义行为空间每个维度的取值范围

        self.observation_space = {
            # 如果有连续值，可以这样定义：
            'feature1': {
                'type': 'continuous',
                'low': 0,
                'high': 256
            },
            'feature2': {
                'type': 'continuous',
                'low': 0,
                'high': 256
            },
            'feature3': {
                'type': 'continuous',
                'low': -180,
                'high': 180
            },
            'feature4': {
                'type': 'continuous',
                'low': -90,
                'high': 90
            },
            'feature5': {
                'type': 'continuous',
                'low': 0,
                'high': 5
            },
            'feature6': {
                'type': 'continuous',
                'low': 0,
                'high': 1
            },
            'feature7': {
                'type': 'continuous',
                'low': 0,
                'high': 10
            },
            'feature8': {'type': 'discrete', 'n': 2}
        }
        self.observation_shape = (len(self.observation_space), )
        # self.observation_num = np.prod(self.observation_dim)

        # self.action_num = np.prod(self.action_dim)
        self.action_space = {
            'type': 'discrete',
            'n': 20,
            'dim': [20,]
        }

        # 连接数据库
        db_path = os.path.join('data', db_name)
        self.conn = sqlite3.connect(db_path)
        self.cursor = self.conn.cursor()


        # 获取所有唯一的状态
        self.all_train_states = self.get_all_train_states()
        self.num_train_states = len(self.all_train_states)
        self.idx_state = 0
 
        # 当前状态
        self.state = None

        # 用于跟踪环境终止后智能体继续执行的步数的变量
        self.steps_beyond_terminated = None

        # 初始化随机数生成器，使用默认种子0
        self.seed(seed)


    
    def step(self, action):
        # 动作与状态检查
        err_msg = f"{action!r} ({type(action)}) invalid"
        assert 0 <= action < self.action_space['n'], err_msg
        assert self.state is not None, "Call reset before using step method."  

        # 判断终止条件
        terminated = bool(
            self.idx_state >= self.num_train_states - 1
        )

        current_action = self.index_to_action(action)
        self.idx_state += 1

        if not terminated:
            reward = self.calculate_reward(self.state, current_action)
            self.state = self.get_state(self.idx_state)
        elif self.steps_beyond_terminated is None:
            # 智能体尝试在终止状态执行动作
            self.steps_beyond_terminated = 0
            reward = self.calculate_reward(self.state, current_action)
        else:
            if self.steps_beyond_terminated == 0:
                logging.warning(
                    "You are calling 'step()' even though this "
                    "environment has already returned terminated = True. You "
                    "should always call 'reset()' once you receive 'terminated = "
                    "True' -- any further steps are undefined behavior."
                )
            self.steps_beyond_terminated += 1
        
        return self.state, reward, terminated, {}

    def reset(self):
        # 使用独立的随机数生成器而不是全局的np.random
        # self.current_row = int(self.rng.integers(0, self.action_space['n']))
        self.idx_state = 0

        self.state = self.get_state(self.idx_state)

        self.steps_beyond_terminated = None

        # print(f"当前状态: state={self.state}")
        return self.state

    def get_state(self, row_index):
        """获取指定行的状态参数"""
        if 0 <= row_index < self.num_train_states:
            state = self.all_train_states[row_index]
            return state
        return None


    def index_to_action(self, action_index):
        """将一维动作索引转换为数据库中的动作值"""
        # 根据你的互斥二维动作映射逻辑
        if 0 <= action_index <= 9:
            dim1_value = (action_index + 1) * 0.1
            action_tuple = (round(dim1_value, 1), 0)
        else: # 10 <= action_index <= 19
            dim2_value = action_index - 9
            action_tuple = (0, dim2_value)

        # 关键步骤：将元组转换为指定格式的 NumPy 数组
        return np.array(action_tuple, dtype=np.float64)

    def calculate_reward(self, state, action):
        # 动作现在是一个二维数组，例如 np.array([0.8, 0.0])
        # 状态包含8个特征
        
        # 使用 SELECT EXISTS 检查记录是否存在
        # 这比检索数据更高效，因为它在找到第一个匹配项时就会停止
        query = '''
        SELECT EXISTS(
            SELECT 1
            FROM train_table
            WHERE "feature1"=? AND "feature2"=? AND "feature3"=? AND "feature4"=? 
            AND "feature5"=? AND "feature6"=? AND "feature7"=? AND "feature8"=?
            AND "action1"=? AND "action2"=?
        )
        '''
        
        # 将 state 和 action 的值拼接成一个完整的参数元组
        # 这是关键步骤，确保了参数的顺序与 SQL 查询中的占位符一一对应
        params = tuple(state) + tuple(action)
        # print(params)
        try:
            self.cursor.execute(query, params)
            result = self.cursor.fetchone()
            
            # result[0] 会是 1 (存在)） 或 0 (不存在)
            if result is not None and result[0] == 1:
                # 如果找到匹配的记录，返回一个正奖励
                return 1.0  
            else:
                # 如果未找到，返回一个负奖励或惩罚
                # print(f"未找到匹配的记录: state={state}, action={action}")
                return -0.1
                
        except Exception as e:
            # 捕获可能的数据库查询错误
            print(f"数据库查询出错: {e}")
            raise

    def action_sample(self):
        # 使用独立的随机数生成器
        return int(self.rng.integers(0, self.action_space['n']))



    def close(self):
        """关闭环境，释放资源"""
        if hasattr(self, 'cursor') and self.cursor is not None:
            self.cursor.close()
        if hasattr(self, 'conn') and self.conn is not None:
            self.conn.close()

    def seed(self, seed):
         # 创建独立的随机数生成器
        seed_seq = np.random.SeedSequence(seed)
        self.rng = np.random.Generator(np.random.PCG64(seed_seq))
        
        return [seed]

    def get_all_train_states(self, db_path='data/train_data.db'):
        """
        从训练数据库中获取所有唯一的状态

        Args:
            db_path: 训练数据库路径
        
        Returns:
            list: 所有唯一的状态列表
        """
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有唯一的状态组合
        query = '''
        SELECT DISTINCT "feature1", "feature2", "feature3", "feature4", "feature5", "feature6", "feature7", "feature8"
        FROM train_table
        ORDER BY "feature1", "feature2", "feature3", "feature4", "feature5", "feature6", "feature7", "feature8"
        '''
        
        cursor.execute(query)
        states = cursor.fetchall()
        
        # 转换为numpy数组
        states_array = np.array(states, dtype=np.float64)
        
        conn.close()
        
        # print(f"📊 从测试数据库中找到 {len(states_array)} 个唯一状态")
        return states_array
