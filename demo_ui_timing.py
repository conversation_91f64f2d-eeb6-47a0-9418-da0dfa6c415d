#!/usr/bin/env python3
"""
演示UI时间统计功能的脚本
"""
import tkinter as tk
from tkinter import messagebox
import time
import threading

# 模拟的测试函数
def simulate_single_test():
    """模拟单个测试"""
    time.sleep(0.1)  # 模拟计算时间
    return "单个测试完成"

def simulate_batch_test():
    """模拟批量测试"""
    time.sleep(2.0)  # 模拟批量计算时间
    return "批量测试完成"

def simulate_manual_test():
    """模拟手动测试"""
    time.sleep(0.05)  # 模拟手动测试时间
    return "手动测试完成"

class TimingDemoApp:
    def __init__(self, root):
        self.root = root
        self.root.title("时间统计功能演示")
        self.root.geometry("600x400")
        
        self.create_widgets()

    def create_widgets(self):
        # 创建主框架
        main_frame = tk.Frame(self.root, padx=10, pady=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 运行时间显示区域
        time_frame = tk.LabelFrame(main_frame, text="运行时间统计", padx=10, pady=5)
        time_frame.pack(fill=tk.X, pady=5)
        
        # 创建时间显示标签
        self.time_labels = {}
        time_info_frame = tk.Frame(time_frame)
        time_info_frame.pack(fill=tk.X)
        
        # 单个测试时间
        single_time_frame = tk.Frame(time_info_frame)
        single_time_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        tk.Label(single_time_frame, text="单个测试:").pack(anchor=tk.W)
        self.time_labels['single'] = tk.Label(single_time_frame, text="未运行", fg="gray")
        self.time_labels['single'].pack(anchor=tk.W)
        
        # 批量测试时间
        batch_time_frame = tk.Frame(time_info_frame)
        batch_time_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        tk.Label(batch_time_frame, text="批量测试:").pack(anchor=tk.W)
        self.time_labels['batch'] = tk.Label(batch_time_frame, text="未运行", fg="gray")
        self.time_labels['batch'].pack(anchor=tk.W)
        
        # 手动测试时间
        manual_time_frame = tk.Frame(time_info_frame)
        manual_time_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)
        tk.Label(manual_time_frame, text="手动测试:").pack(anchor=tk.W)
        self.time_labels['manual'] = tk.Label(manual_time_frame, text="未运行", fg="gray")
        self.time_labels['manual'].pack(anchor=tk.W)

        # 结果显示区域
        self.results_text = tk.Text(main_frame, height=15, width=70)
        self.results_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # 按钮区域
        button_frame = tk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=5)

        self.single_button = tk.Button(button_frame, text="运行单个测试", command=self.run_single_test)
        self.single_button.pack(side=tk.LEFT, padx=5)

        self.batch_button = tk.Button(button_frame, text="运行批量测试", command=self.run_batch_test)
        self.batch_button.pack(side=tk.LEFT, padx=5)

        self.manual_button = tk.Button(button_frame, text="运行手动测试", command=self.run_manual_test)
        self.manual_button.pack(side=tk.LEFT, padx=5)

        self.clear_button = tk.Button(button_frame, text="清空结果", command=self.clear_results)
        self.clear_button.pack(side=tk.RIGHT, padx=5)

    def _update_time_display(self, test_type, elapsed_time):
        """更新时间显示"""
        time_text = f"{elapsed_time:.3f} 秒"
        self.time_labels[test_type].config(text=time_text, fg="blue")

    def _add_result(self, text):
        """添加结果到显示区域"""
        self.results_text.insert(tk.END, text + "\n")
        self.results_text.see(tk.END)

    def run_single_test(self):
        """运行单个测试"""
        self.single_button.config(state=tk.DISABLED)
        
        def test_thread():
            start_time = time.time()
            self._add_result("开始单个测试...")
            
            result = simulate_single_test()
            
            elapsed_time = time.time() - start_time
            self._update_time_display('single', elapsed_time)
            self._add_result(f"单个测试完成，用时: {elapsed_time:.3f} 秒")
            self._add_result("-" * 40)
            
            self.single_button.config(state=tk.NORMAL)
        
        threading.Thread(target=test_thread, daemon=True).start()

    def run_batch_test(self):
        """运行批量测试"""
        self.batch_button.config(state=tk.DISABLED)
        
        def test_thread():
            start_time = time.time()
            self._add_result("开始批量测试...")
            
            result = simulate_batch_test()
            
            elapsed_time = time.time() - start_time
            self._update_time_display('batch', elapsed_time)
            self._add_result(f"批量测试完成，用时: {elapsed_time:.3f} 秒")
            self._add_result("-" * 40)
            
            self.batch_button.config(state=tk.NORMAL)
        
        threading.Thread(target=test_thread, daemon=True).start()

    def run_manual_test(self):
        """运行手动测试"""
        self.manual_button.config(state=tk.DISABLED)
        
        def test_thread():
            start_time = time.time()
            self._add_result("开始手动测试...")
            
            result = simulate_manual_test()
            
            elapsed_time = time.time() - start_time
            self._update_time_display('manual', elapsed_time)
            self._add_result(f"手动测试完成，用时: {elapsed_time:.3f} 秒")
            self._add_result("-" * 40)
            
            self.manual_button.config(state=tk.NORMAL)
        
        threading.Thread(target=test_thread, daemon=True).start()

    def clear_results(self):
        """清空结果"""
        self.results_text.delete('1.0', tk.END)
        # 重置时间显示
        for label in self.time_labels.values():
            label.config(text="未运行", fg="gray")

if __name__ == '__main__':
    root = tk.Tk()
    app = TimingDemoApp(root)
    
    # 添加欢迎信息
    app._add_result("=== 时间统计功能演示 ===")
    app._add_result("点击按钮测试各种功能的运行时间统计")
    app._add_result("时间将显示在上方的统计区域中")
    app._add_result("-" * 40)
    
    root.mainloop()
