# 数据处理
`data_manage.py`对原始数据进行处理：
1. 分块数据导入：脚本能够高效处理大型CSV文件。通过使用pandas的chunksize参数，它逐块读取数据并导入到主数据库中，有效避免了因内存不足导致程序崩溃的问题。

2. 特征归一化：在数据导入过程中，对特定列的数据进行了特定的归一化处理。这对于许多机器学习算法至关重要，能确保不同量纲的特征在模型训练中得到公平对待。

3. 基于特征组的划分：这是该脚本的关键创新点。它不简单地按行随机划分数据，而是首先根据feature1到feature8的组合生成一个唯一的**group_id**。然后，它以这个group_id为单位，将所有的数据划分为训练集和测试集。这种划分方式保证了属于同一个“特征组”的所有数据点（可能来自同一回合或同一实验）要么全部在训练集中，要么全部在测试集中，避免了数据泄露**（Data Leakage）问题。

4. 独立的数据库存储：处理后的训练集和测试集数据被分别存储在各自独立的SQLite数据库文件中（train_data.db和test_data.db）。这使得后续的模型训练和评估流程更加清晰和独立，易于管理。

5. 索引创建：为了提高后续数据查询（特别是用于模型训练时的数据加载）的效率，脚本在训练和测试数据库中创建了复合索引。

6. 可重复性：通过设置随机种子（SEED），确保每次运行脚本时，数据集的划分结果都是完全一致的，这对于科学实验的可重复性至关重要。

# 环境
`env.py`是强化学习的环境文件，该环境的设计基于一个离线强化学习（Offline RL）的场景。它不是一个交互式环境，即智能体不能通过与环境互动来产生新的数据。相反，它依赖于一个预先构建好的、存储在SQLite数据库中的静态数据集。智能体的目标是利用这些数据，学习一个最优的策略。
## 状态空间
状态空间由八个特征组成，这些特征共同定义了环境的当前状态。这些特征的详细信息如下：

- feature1：网络大小
- feature2：邻居节点数
- feature3: 经度
- feature4: 维度
- feature5: 平均连接度
- feature6: 邻居节点变化率
- feature7: 数据速率
- feature8: 路由协议，0：AODV，1：OLSR

WaveDecideEnv的环境状态是离散且有限的，因为所有可能的状态都来自预先处理好的数据库中的唯一组合。
## 动作空间
动作空间是离散型的，由20个可选的路由协议参数组成，从1到20。智能体需要从这20个值中选择一个作为动作。

- action1: AODV路由发现时间，0.1、0.2、0.3、... 、1.0 s
- action2: OLSR拓扑控制间隙，1、2、3、 ... 、10 s
两个动作参数互斥（同时只有一个有取值）
## 奖励机制
环境的奖励机制设计得非常清晰，由于数据库直接给出每个状态下的最优动作，所以采用离散奖励。

- 奖励计算公式：reward = 1

    - 这个公式表明，当采取最佳动作时奖励为1

- 惩罚机制：

    - 如果智能体采取非最优的动作，则在数据库中查询不到，则会返回一个负奖励（-0.1）。

环境交互流程
1. 初始化 (__init__):

- 环境加载预处理好的train_data.db数据库。

- 从数据库中读取所有唯一的特征组合，并将其存储为所有训练状态的列表。

2. 重置 (reset):

- 将环境的状态重置为第一个训练状态，并返回该状态。

- 这使得每个训练回合都从一个固定的起始点开始，便于调试和评估。

3. 步进 (step):

- 智能体提供一个动作索引（0-19）。

- 环境将这个索引转换为数据库中的实际动作值。

- 使用当前状态和动作，从数据库中查询是否存在一组这样的数据。

- 如果存在则返回奖励1，否则返回-0.1。

- 将状态更新为列表中的下一个状态。

- 当遍历完所有状态后，环境终止（terminated = True）。

# 经验回放
`memory.py` 文件实现了两种用于强化学习训练的经验回放缓存（Replay Buffer），它们是用于存储、管理和采样智能体与环境交互数据（即经验）的核心组件。
1. MultiStepBuff (多步缓冲区)
MultiStepBuff是一个双端队列（deque），主要用于**n-步奖励（n-step return）**的计算。

- 功能: 它临时存储最近的 n 个状态、动作和奖励。

- n-步奖励: 传统的强化学习算法通常使用单步奖励来更新Q值，而n-步方法则向前看 n 步，将这 n 步的奖励按折扣因子累加起来，从而获得一个更长远的奖励信号。这有助于解决稀疏奖励（Sparse Reward）问题，即奖励信号不频繁出现的情况。

- 工作机制:

    - append(): 接收一个单步经验并将其添加到队列中。

    - get(): 当队列满时，它会移除最旧的经验，并计算该经验对应的n-步奖励，然后返回这个包含累加奖励的经验元组。

    - _nstep_return(): 负责执行奖励的累加计算。


2. LazyMemory (懒惰内存)
LazyMemory是该文件中的基础经验回放缓存。它的设计考虑到了高效的内存管理和随机采样。

- 特点: 它将经验数据中的大型对象（如状态）和小型对象（如动作、奖励）分开存储。

    - 状态（state, next_state）被存储在Python的列表 (list) 中，这允许灵活地处理不同形状或大小的数据，例如图像或可变长度的观测值。

    - 动作、奖励和完成标志（action, reward, done）被存储在固定大小的NumPy数组 (np.empty) 中，这对于快速、向量化的存取和采样非常高效。

- 核心机制:

    - 循环缓存: 使用_p索引来记录下一个写入位置，当达到容量上限时，它会从头开始覆盖最旧的经验，实现循环存储。

    - 高效采样: sample()方法通过np.random.randint()快速生成一个批次的随机索引，然后利用这些索引从NumPy数组和列表中提取数据，并将它们转换为PyTorch张量 (torch.FloatTensor)，以便直接用于模型训练。

3. LazyMultiStepMemory (多步懒惰内存)
这个类是LazyMemory的扩展，它将MultiStepBuff的功能集成进来，从而实现了支持n-步奖励计算的经验回放缓存。

- 集成逻辑:

    - 当multi_step参数不等于1时，它会使用MultiStepBuff作为内部缓冲区。

    - 每当append()一个新经验时，它首先被存入MultiStepBuff。

    - 当MultiStepBuff满时，它会计算并弹出一个完整的 n-步经验，然后将这个完整的经验元组（包含累加奖励）存储到LazyMemory的底层结构中。

    - 当一个回合结束（done=True）时，它会处理MultiStepBuff中剩余的所有未满 n 步的经验，并将其存储。

- 优势: 这种设计结合了 LazyMemory 的高效存储和 MultiStepBuff 的 n-步奖励计算，为训练提供更稳定、更丰富的奖励信号，有助于加速模型收敛。

# 模型架构
`model.py` 文件定义了用于训练强化学习智能体的各种神经网络模型。这些模型被设计用于处理离散动作空间，并且包含了多种先进的架构，如双Q网络（Twinned Q-network）和对偶网络（Dueling Network）。
1. QNetwork
QNetwork用于估计在给定状态下执行每个离散动作的Q值（即期望的未来回报）。

- 标准架构：

    - 网络由多个全连接层（nn.Linear）和ReLU激活函数组成，这是一个典型的多层感知机（MLP）结构。

    - 输入是状态的维度 state_dim，输出是动作的数量 num_actions。

    - 每个输出神经元代表一个动作的Q值。

- 对偶网络 (dueling_net=True)：

    - 这是一种更高级的Q网络架构，旨在更有效地估计Q值。

    - 它将网络分成两个独立的流：

        - 优势函数头部 (a_head)：估计每个动作的优势（Advantage），即执行某个动作比平均动作好多少。

        - 状态值函数头部 (v_head)：估计当前状态的价值（Value），即在当前状态下能获得的平均回报。

    - 最终的Q值由这两部分组合而成


    - 这种设计能让网络更好地解耦状态价值和动作价值，从而提高学习效率。

2. TwinnedQNetwork
TwinnedQNetwork实现了双Q网络（或称双头Q网络）架构。

- 功能：它包含两个独立的 QNetwork 实例，分别命名为 Q1 和 Q2。

- 作用：在许多强化学习算法（如SAC、TD3）中，使用两个Q网络可以有效缓解Q值过高估计的问题。

- 工作机制：在计算目标Q值时，通常会取两个网络预测值的最小值，这能提供一个更保守、更稳定的目标值，从而提高学习的鲁棒性。

3. CategoricalPolicy
CategoricalPolicy是一个专门为离散动作空间设计的策略网络。

- 功能：该网络学习一个策略（Policy），即在每个状态下选择不同动作的概率分布。

- 工作机制：

    - 网络输入为状态，输出为每个动作的logits（原始分数）。

    - sample()方法：

        - 使用 Softmax 函数将 logits 转换为概率分布。

        - 利用 PyTorch 的 Categorical 分布对象，根据这些概率随机采样一个动作。这使得智能体可以在探索（随机性）和利用（选择高概率动作）之间取得平衡。

        - 它还返回动作的概率及其对数概率，这对于一些基于策略梯度的算法（如SAC）非常关键。

    - act()方法：

        - 这是一个用于评估或测试的确定性方法。

        - 它直接选择概率最高的动作（即 argmax），而不会进行随机采样。

>[!note]  
返回的动作标签是 0 到 num_actions - 1 之间的整数

# 智能体
`base.py` 和 `sacd.py` 这两个文件共同定义了一个完整的强化学习智能体（Agent）框架，其中 base.py 提供了通用的、抽象的骨架，而 sacd.py 则具体实现了**SAC-D（Soft Actor-Critic for Discrete Actions）**算法。

1. base.py：智能体抽象基类
base.py 文件定义了一个名为 BaseAgent 的抽象基类，它提供了所有强化学习智能体都应具备的通用功能和结构。这个设计模式使得代码具有高度的复用性和可扩展性，你可以在此基础上轻松实现其他算法，而无需重写基础的训练流程、日志记录和评估逻辑。

核心功能：

- 初始化和配置：

    - __init__ 方法接收大量的超参数（如步数、批次大小、折扣因子、内存大小等），并初始化智能体所需的所有核心组件。

    - 它初始化了训练和测试环境、LazyMultiStepMemory 经验回放缓存、TensorBoard 日志记录器和训练回报的滑动平均统计器。

    - 通过设置随机种子，确保实验的可重复性。

- 训练和评估循环：

    - run()：这是主要的训练循环，会一直运行直到达到设定的总步数，并在此期间调用 train_episode。

    - train_episode()：处理一个完整的回合训练。它控制智能体的探索（在开始阶段随机探索，之后使用策略网络）和与环境的交互，并将经验存入回放缓存。

    - learn()：是核心的学习方法。它从缓存中采样一个批次的经验，并调用抽象方法来计算并更新策略网络、Q网络和熵参数。

    - evaluate()：定期评估智能体在测试环境中的表现，并保存性能最好的模型。

- 抽象方法 (@abstractmethod)：

    - BaseAgent 定义了一系列抽象方法，这些方法必须由继承它的具体智能体类来实现。这包括：

        - explore() 和 exploit()：分别定义了探索（带随机性）和利用（确定性）的动作选择策略。

        - update_target()：定义了目标网络的更新方式。

        - calc_..._loss()：定义了各种网络损失的计算方法。

        - save_models()：定义了模型的保存逻辑。

2. sacd.py：SAC-D 算法实现
sacd.py 文件继承了 BaseAgent，并实现了 **SAC-D（Soft Actor-Critic for Discrete Actions）**算法。它通过具体实现 BaseAgent 中的抽象方法，将通用的智能体框架转化为一个可执行的、用于离散动作空间的SAC智能体。

核心功能：

- 网络定义：

    - 它实例化了 model.py 中定义的网络模型：一个用于策略的 CategoricalPolicy 和两个用于Q值的 TwinnedQNetwork（一个在线，一个目标网络）。

    - 这实现了 SAC 算法中的双Q网络和策略网络架构。

    - 它将在线评论家网络的参数复制到目标评论家网络，并禁用目标网络的梯度计算。

- 优化器与温度参数：

    - 使用 Adam 优化器来更新策略网络、两个Q网络以及温度参数 alpha。

    - alpha 是SAC算法中的关键参数，用于平衡探索与利用。该脚本通过优化 log_alpha 来自动调整 alpha，从而无需手动调整这个超参数。

- 方法实现：

    - explore()：通过策略网络进行随机采样来选择动作，用于训练过程中的探索。

    - exploit()：通过策略网络选择具有最高概率的动作，用于评估过程中的确定性行为。

    - update_target()：使用软更新（Soft Update）的方式，将在线Q网络的参数缓慢地更新到目标Q网络，以稳定训练。

    - calc_critic_loss()：计算两个Q网络的损失，通过比较当前Q值和由贝尔曼方程计算出的目标Q值来得到均方误差。

    - calc_policy_loss()：计算策略网络的损失。该损失函数旨在最大化 (Q值 + alpha * 熵)，鼓励智能体在获得高奖励的同时保持一定的探索性。

    - calc_entropy_loss()：计算熵损失，用于自动调整 alpha。当策略的熵低于目标熵时，alpha 会增加，反之则减少。


# 训练
`train.py`是训练过程启动脚本，它会获取训练所需的参数，大部分参数保存在./config/sacd.yaml文件中，是否使用GPU以及随机种子可以在终端进行设定，语法是

'''shell  
python train.py --cuda --seed 42


# 测试
`test.py`和`waveDecide_RL.py`都是使用训练好的策略模型进行决策的脚本文件。
不同点是：
- `test.py`目的是为了测试模型是否有效，以及测试模型参数加载、测试状态获取、模型推理三个函数的功能是否正常。
- `waveDecide_RL.py`的目的是使用训练好的模型进行推理，它支持三种模式分别是：
    - 直接设定一个干扰状态，并推理该状态下最优的通信波形
    - 获取测试集中所有的干扰状态，并推理所有状态下的最优通信波形
    - 通过终端输入一个干扰状态，并推理最优波形
>[!note]  
在实际使用中，接受其他模块发送来的一组干扰参数并进行推理时，可直接调用`waveDecide_RL.py`模块中的`waveDecide_RL`函数进行波形决策。

>[!important]  
目前`waveDecide_RL.py`并没有统计正确率，这是需要改进的地方。

