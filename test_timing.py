#!/usr/bin/env python3
"""
测试时间统计功能的简单脚本
"""
import time
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from routeDecide_RL import routeDecide_RL
    import torch
    
    print("=== 时间统计功能测试 ===")
    
    # 测试单个推理的时间
    interference = [150, 86, 78.37, 33.19, 0.45, 0.33, 0.33, 'AODV']
    
    print(f"测试参数: {interference}")
    
    # 进行多次测试以获得平均时间
    times = []
    num_tests = 10
    
    print(f"\n进行 {num_tests} 次测试...")
    
    for i in range(num_tests):
        start_time = time.time()
        
        with torch.no_grad():
            action = routeDecide_RL(interference, Normalized=False)
        
        elapsed_time = time.time() - start_time
        times.append(elapsed_time)
        
        print(f"测试 {i+1}: {elapsed_time:.4f} 秒, 结果: {action}")
    
    # 统计结果
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    print(f"\n=== 统计结果 ===")
    print(f"平均时间: {avg_time:.4f} 秒")
    print(f"最短时间: {min_time:.4f} 秒")
    print(f"最长时间: {max_time:.4f} 秒")
    print(f"时间范围: {max_time - min_time:.4f} 秒")
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保 routeDecide_RL.py 文件存在且可导入")
except Exception as e:
    print(f"运行错误: {e}")
